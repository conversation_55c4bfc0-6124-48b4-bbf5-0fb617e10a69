<template>
  <div class="container">
    <div class="first-titles">年度总体目标</div>
    <div class="boxContainer">
      <div class="boxOne" v-for="(item, index) in ztmbData" :key="index">
        <div class="boxName" slot="reference">{{ item.name }}</div>

        <div class="numWrap">
          <div class="number" v-for="(item, i) in item.number" :key="i">
            <div class="numbg" v-if="item != ',' && item != '.' && item != '-'">
              <span>{{ Number(item) }}</span>
            </div>
            <div class="numA" v-else>{{ item }}</div>
          </div>
          <div class="unitA">{{ item.unit }}</div>
        </div>
        <div class="datLine">
          <div class="datLeft">
            <div class="labelClass">目标值：</div>
            <div class="numB">{{ item.mbz }}{{ item.mbzUnit }}</div>
          </div>
          <div class="datLeft">
            <div class="labelClass">完成度：</div>
            <div class="numC">{{ item.wcd }}%</div>
          </div>
        </div>
        <div class="jdChartClass" :id="`hjfb-chart${index + 1}`"></div>
      </div>
    </div>

    <div class="first-titles">产业风险预警</div>
    <div class="table">
      <div class="con" style="height: 100%; justify-content: normal">
        <div class="con-item" style="width: 30%; display: flex; flex-direction: column; justify-content: space-evenly">
          <div class="lis_box s-c-white s-font-32 s-flex s-col-center" v-for="(ele, index) in listData" :key="index">
            <div style="display: flex; flex-direction: column; align-items: center">
              <img
                @click="openqyfx(ele.tit)"
                class="cursorP"
                :src="require(`@/pages/qyzfCyl/img/qyhx/fxyj_right${index}.png`)"
                alt=""
              />
            </div>
            <div
              style="font-size: 52px; font-weight: bold"
              @click="changeData(index, ele.tit)"
              :class="(fxyjIndex1 == index && 'num_bgactive') || 'num_bg'"
            >
              <span style="font-weight: normal; font-size: 36px; color: #dedede">{{ ele.tit }}</span>
              <br />
              <span :style="{ color: index == fxyjIndex1 ? 'rgb(64 163 224)' : '' }">
                {{ Number(ele.num).toLocaleString() }}
              </span>
              <span class="unit">{{ ele.unit }}</span>
            </div>
          </div>
        </div>
        <div class="con-item" style="flex: 1">
          <div class="s-flex-1 overHide" style="position: relative">
            <div class="qyyjqy">
              <div class="qyyjqy_top s-row-center">
                <div class="qyyjqy_top_1">总数</div>
                <div class="qyyjqy_top_2">
                  <div class="s-w7">
                    <span>{{ zstotal }}</span>
                  </div>
                  <span style="margin-left: 10px">{{ listData[fxyjIndex1].unit }}</span>
                </div>
              </div>
              <div v-show="fxyjIndex1 == 0" class="cyzyClass">
                <div class="tableWrap">
                  <table>
                    <thead>
                      <tr class="table-head table-bg-1">
                        <th class="wid100">序号</th>
                        <th class="wid240 newWid">
                          <div class="wzTxt">所属区县</div>
                          <el-popover placement="top-start" trigger="hover" content="">
                            <div class="optionClass">
                              <div
                                @click="filterSsqx(item)"
                                :class="{ optionItem: true, ssqxActiveClass: item == ssqxActive }"
                                v-for="(item, index) in ssqxOption"
                                :key="index"
                              >
                                {{ item }}
                              </div>
                            </div>
                            <i slot="reference" class="jtClass cursorP el-icon-caret-bottom"></i>
                          </el-popover>
                        </th>
                        <th class="wid240 newWid">
                          <div class="wzTxt">风险详情</div>
                          <el-popover placement="top-start" trigger="hover" content="">
                            <div class="optionClass">
                              <div
                                @click="filterYjnr(item)"
                                :class="{ optionItem: true, ssqxActiveClass: item == yjnrActive }"
                                v-for="(item, index) in yjnrOption"
                                :key="index"
                              >
                                {{ item }}
                              </div>
                            </div>
                            <i slot="reference" class="jtClass cursorP el-icon-caret-bottom"></i>
                          </el-popover>
                        </th>
                        <th class="wid140">风险等级</th>
                        <th class="wid400">涉事企业</th>
                        <th class="wid240">涉事时间</th>
                      </tr>
                    </thead>
                    <tbody
                      class="tbody"
                      id="scrollTbody"
                      @mouseover="mouseenterEvent()"
                      @mouseleave="mouseleaveEvent()"
                    >
                      <tr
                        :class="{ 'table-bg-1': index % 2 != 0, 'table-bg-2': index % 2 == 0, 'table-content': true }"
                        v-for="(item, index) in jyfxList"
                        :key="index"
                      >
                        <td class="wid100 overClassP">{{ index + 1 }}</td>
                        <td class="wid240 overClassP">{{ item.ssqx }}</td>
                        <td class="wid240 overClassP">{{ item.yjnr }}</td>
                        <td
                          :class="{
                            wid140: true,
                            overClassP: true,
                            gao: item.fxpf == '高',
                            zhong: item.fxpf == '中',
                            di: item.fxpf == '低',
                          }"
                        >
                          {{ item.fxpf }}
                        </td>
                        <td class="wid400 overClassP cursorP qymcClass">{{ item.qymc }}</td>
                        <td class="wid240 overClassP">{{ item.yjsj }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="pageWrap">
                  <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParams.pageNum"
                    :page-sizes="[10, 20, 40]"
                    :page-size="queryParams.pageSize"
                    layout="total, prev, pager, next"
                    :total="total"
                  />
                </div>
              </div>
              <div v-show="fxyjIndex1 != 0" id="echart06" style="width: 100%; height: 600px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface } from '@/api/csdnIndexApi'
import { getIrsData } from '@/api/qyzf/qyhx'
export default {
  name: 'CylLeft',
  data() {
    return {
      ztmbData: [
        {
          name: '规上总产值',
          number: '4155.6',
          unit: '亿元',
          mbz: '4500',
          mbzUnit: '亿元',
          wcd: '92.35',
        },
        {
          name: '制造业投资增速',
          number: '8.4',
          unit: '%',
          mbz: '11',
          mbzUnit: '%',
          wcd: '76.36',
        },
        {
          name: '招引十亿元以上重大项目',
          number: '59',
          unit: '个',
          mbz: '25',
          mbzUnit: '个',
          wcd: '236',
        },
        {
          name: '培育“链主”企业',
          number: '65',
          unit: '家',
          mbz: '40',
          mbzUnit: '家',
          wcd: '162.5',
        },
        {
          name: '开展关键技术难题攻关',
          number: '30',
          unit: '项',
          mbz: '15',
          mbzUnit: '项',
          wcd: '200',
        },
        {
          name: '新增科创平台',
          number: '79',
          unit: '个',
          mbz: '50',
          mbzUnit: '个',
          wcd: '158',
        },
        {
          name: '培育省级以上专精特新和隐形冠军企业',
          number: '84',
          unit: '家',
          mbz: '40',
          mbzUnit: '家',
          wcd: '210',
        },
        {
          name: '新增金华“品字标”企业',
          number: '96',
          unit: '家',
          mbz: '20',
          mbzUnit: '家',
          wcd: '480',
        },
      ],
      listData: [
        { tit: '用能风险', unit: '条', num: 0 },
        { tit: '安全生产风险', unit: '家', num: 0 },
        { tit: '信用风险', unit: '家', num: 0 },
        { tit: '外迁风险', unit: '家', num: 0 },
      ],
      fxyjIndex1: 0,
      zstotal: 0,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ssqx: null,
        yjnr: null,
        // yjnr: "'用电骤降','用电骤升'"
        // yjnr: "'用电骤升'"
      },
      jyfxList: [],
      ssqxOption: [
        '东阳市',
        '义乌市',
        '兰溪市',
        '婺城区',
        '开发区',
        '武义县',
        '永康市',
        '浦江县',
        '磐安县',
        '金东区',
        '市本级',
      ],
      ssqxActive: '',
      yjnrOption: ['用水骤升', '用水骤降', '用电骤升', '用电骤降'],
      yjnrActive: '',
      scrollTbody: null,
      time: null,
    }
  },

  async mounted() {
    this.getNdztMb()
    this.getCyfxyj()
    await this.getYnFX()
    this.zstotal = this.listData[0].num
    this.scrollTbody = document.getElementById('scrollTbody')
    this.mouseleaveEvent()
  },

  methods: {
    getNdztMb() {
      getCsdnInterface('csdn_qyhx67', { sstp: '100' }).then((res) => {
        this.ztmbData[0].number = res.data.data[0].gszcz
        this.ztmbData[0].mbz = res.data.data[0].gszczmb
        this.ztmbData[0].wcd = res.data.data[0].gszczmbwcd
        console.log('获取年度总体目标', res, this.ztmbData)

        let arr = this.ztmbData.map((el) => {
          // let str = el.wcd.slice(0, -1)
          let str = el.wcd
          return str == '-' ? 0 : Number(str)
        })
        // console.log('完成度', arr)

        arr.forEach((item, index) => {
          this.setChart(`hjfb-chart${index + 1}`, item)
        })
      })
    },

    getCyfxyj() {
      getCsdnInterface('qyhx_cyl_fxyjsltj').then((res) => {
        this.listData[1].num = res.data.data.find((it) => it.ywwd1 == '安全生产风险').tjz
        this.listData[2].num = res.data.data.find((it) => it.ywwd1 == '信用风险').tjz
        this.listData[3].num = res.data.data.find((it) => it.ywwd1 == '外迁风险').tjz
      })
    },

    async getYnFX() {
      let _this = this
      let params = {
        name: '金华市城市大脑企业智服产业链用能风险信息',
        url: 'http://dw.jinhua.gov.cn/micoservice-provider/xn94lLa9aYe5df20.htm',
        params: _this.queryParams,
      }
      getIrsData(params).then((res) => {
        console.log('获取用能风险信息', res)
        if (res.status == 200) {
          _this.jyfxList = res.data.data.list
          _this.total = res.data.data.total
          _this.zstotal = res.data.data.total
          _this.listData[0].num = res.data.data.total
        }
      })
    },

    async changeData(index, name) {
      // 防止已选中后，重复点击
      if (this.fxyjIndex1 == index) {
        return
      }
      this.fxyjIndex1 = index
      if (this.fxyjIndex1 == 0) {
        this.resetQuery()
        await this.getYnFX()
        this.zstotal = this.listData[0].num
      } else {
        let data = []
        getCsdnInterface('qyhx_cyl_fxyjtjfcyl', { ywwd1: name }).then((res) => {
          data = res.data.data.map((el) => {
            return {
              name: el.ywwd2,
              value: Number(el.tjz),
            }
          })
          this.getChart02('echart06', data, this.listData[this.fxyjIndex1].unit)
        })
        this.zstotal = this.listData[this.fxyjIndex1].num
      }
    },

    async filterSsqx(item) {
      if (this.ssqxActive == item) {
        this.ssqxActive = ''
        this.queryParams.ssqx = null
        this.queryParams.pageNum = 1
        await this.getYnFX()
        this.zstotal = this.listData[0].num
      } else {
        this.ssqxActive = item
        this.queryParams.ssqx = item
        this.queryParams.pageNum = 1
        await this.getYnFX()
        this.zstotal = this.listData[0].num
      }
    },

    async filterYjnr(item) {
      if (this.yjnrActive == item) {
        this.yjnrActive = ''
        this.queryParams.yjnr = null
        this.queryParams.pageNum = 1
        await this.getYnFX()
        this.zstotal = this.listData[0].num
      } else {
        this.yjnrActive = item
        this.queryParams.yjnr = `'${item}'`
        this.queryParams.pageNum = 1
        await this.getYnFX()
        this.zstotal = this.listData[0].num
      }
    },

    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getYnFX()
    },

    handleSizeChange(val) {
      this.queryParams.pageSize = val
      if (this.queryParams.pageNum * val > this.total) {
        this.queryParams.pageNum = 1
      }
      this.getYnFX()
    },

    mouseenterEvent() {
      clearInterval(this.time)
    },

    mouseleaveEvent() {
      console.log('执行了吗', this.scrollTbody.scrollTop, this.scrollTbody.scrollHeight)
      this.time = setInterval(() => {
        this.scrollTbody.scrollTop += 5
        if (this.scrollTbody.scrollTop >= this.scrollTbody.scrollHeight - this.scrollTbody.offsetHeight - 3) {
          this.scrollTbody.scrollTop = 0
        }
      }, 100)
    },

    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ssqx: null,
        yjnr: null,
      }
    },

    openqyfx(name) {
      window.parent.lay.openIframe({
        type: 'openIframe',
        name: 'qyzf-fxyj-dialog',
        src: baseURL.url + '/static/citybrain/qyhx/commont/qyzf-fxyj-dialog.html',
        left: '0',
        top: '0',
        width: '7680px',
        height: '2160px',
        zIndex: '500',
        argument: {
          tit: name,
        },
      })
    },

    getChart02(id, chartData, unit) {
      let myEc = echarts.init(document.getElementById(id))
      let copyData = JSON.parse(JSON.stringify(chartData)).sort(function (a, b) {
        return b.value - a.value
      })
      let maxNum1 = (copyData[0] && copyData[0].value) || 0
      let maxNum2 = (copyData[1] && copyData[1].value) || 0
      let maxNum3 = (copyData[2] && copyData[2].value) || 0
      let data = copyData.map((a) => a.value)
      let colors =
        data.length > 3
          ? data.map((a) =>
              a == maxNum1 ? '#e33c2f' : a == maxNum2 ? '#fcc053' : a == maxNum3 ? '#f58b1a' : '#1677FF'
            )
          : ['#e33c2f', '#fcc053', '#f58b1a']
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          confine: true,
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
          formatter: (e) => {
            let main = e.filter((a) => a.value != '-')[0]
            return `${main.name}<br/>${main.seriesName}：${main.value + unit}`
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '5%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            // data: chartData.map(a => a.name),
            data: copyData.map((a) => a.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,1)', // 颜色
                width: 3, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              // rotate: chartData.length > 9 ? 45 : 0,
              // rotate: 0,
              rotate: -30,
              // width: 100,//将内容的宽度固定
              // overflow: 'truncate',//超出的部分截断
              // overflow: 'break',//超出的部分截断
              // ellipsis: '...',//截断的部分用...代替
              textStyle: {
                color: '#D6E7F9',
                fontSize: 30,
              },
              formatter: (value) => {
                if (value.length > 3) {
                  return value.substring(0, 3) + '...'
                } else {
                  return value
                }
              },
            },
          },
        ],
        yAxis: [
          {
            name: '',
            type: 'value',
            // min: 0,
            nameTextStyle: {
              fontSize: 32,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 32,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '预警企业',
            type: 'bar',
            barWidth: chartData.length > 3 ? '40%' : 50,
            label: {
              normal: {
                show: true,
                position: 'top',
                fontSize: 27,
                color: '#fff',
              },
            },
            itemStyle: {
              normal: {
                color: (param) => {
                  return {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: colors[param.dataIndex],
                      },
                      {
                        offset: 1,
                        color: '#1677FF00',
                      },
                    ],
                  }
                },
                barBorderRadius: 4,
              },
            },
            // data: chartData.map((a) => a.value),
            data: copyData.map((a) => a.value),
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', () => {
        myEc.getZr().setCursorStyle('default')
      })
    },

    setChart(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let option = {
        grid: {
          top: 0,
          left: '0%',
          right: '0%',
          bottom: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          max: 100,
        },
        yAxis: {
          type: 'category',
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 1,
                  y: 1,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(49, 227, 255, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: 'RGBA(7, 103, 190, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 0.9,
                      color: 'RGBA(2, 129, 231, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'RGBA(15, 59, 123, 1)', // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
                opacity: 0.7,
                barBorderRadius: 4,
              },
            },
            label: {
              show: false,
              formatter: '',
              backgroundColor: 'RGBA(25, 194, 254, 1)',
              width: 6,
              height: 6,
              position: 'right',
              offset: [-14, 0],
              borderWidth: 5,
              borderColor: 'RGBA(25, 194, 254, 1)',
              borderRadius: 5,
              shadowColor: 'RGBA(25, 194, 254, 1)',
              shadowBlur: 20,
            },
            showBackground: true,
            backgroundStyle: {
              color: 'RGBA(14, 58, 119, 1)',
            },
            silent: true,
            barWidth: 15,
            barGap: '-100%', // Make series be overlap
            data: [chartData],
          },
          {
            type: 'lines',
            coordinateSystem: 'cartesian2d',
            data: [chartData].map((item, index) => {
              return {
                coords: [
                  [0, index],
                  [item - 4, index],
                ],
              }
            }),
            effect: {
              show: true,
              period: 2.5,
              trailLength: 0,
              symbolSize: 20,
              symbol: 'pin',
              loop: true,
              color: 'RGBA(25, 194, 254, 1)',
            },
            lineStyle: {
              width: 0,
              opacity: 0,
              curveness: 0,
            },
            z: 99,
          },
        ],
      }
      myChart.setOption(option, true)
    },
  },
}
</script>

<style scoped lang="less">
/* 这里可以添加组件的样式 */
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container,
#app {
  width: 2070px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background-color: #000; */
  display: flex;
  flex-direction: column;
}

.first-titles {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 70px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.boxContainer {
  width: 100%;
  flex: 1;
  /* background-color: #20aeff; */
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  grid-column-gap: 45px;
  grid-row-gap: 20px;
}

.boxOne {
  width: 100%;
  height: 100%;
  /* background-color: aquamarine; */
  background-image: url('@/pages/qyzfCyl/img/cyl/boxBg.png');
  background-size: 100% 100%;
  display: flex;

  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 30px 50px 30px 45px;
}

.boxName {
  font-size: 36px;
  color: #ffffff;
  /* line-height: 32px; */
  width: 95%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.numWrap {
  display: flex;
  align-items: baseline;
  /* margin-top: 30px; */
}

.numA {
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 90px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.number {
  display: inline-block;
  /* font-size: 40px;
  color: #fff;
  font-weight: 400; */
}

.number .numbg {
  /* display: inline-block;
  width: 55px;
  height: 75px;
  line-height: 75px;
  text-align: center;
  background: url('/static/citybrain/qyhx/images/index/num.png') no-repeat;
  background-size: contain;
  margin: 0 4px; */
}

.numbg span {
  /* font-size: 90px;
  background: linear-gradient(180deg, #FFC460 0%, #FD852E 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; */
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 90px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.numbgss span {
  font-size: 40px;
  color: #ffffff;
  font-weight: bolder;
}

.anumA {
  font-size: 40px;
  color: #ffffff;
  font-weight: bolder;
}

.aunitA {
  font-size: 32px;
  color: #dcefff;
}

.unitA {
  font-weight: 400;
  font-size: 32px;
  color: #cde7ff;
  letter-spacing: 1px;
  margin-left: 20px;
}

.datLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-top: 30px; */
}

.datLeft {
  display: flex;
  align-items: center;
}

.labelClass {
  font-size: 28px;
  color: #ffffff;
}

.numB {
  font-size: 28px;
  color: rgba(247, 186, 30, 1);
}

.numC {
  font-size: 28px;
  color: rgba(20, 201, 201, 1);
}

.jdChartClass {
  width: 100%;
  height: 15px;
  /* background-color: #FFC460; */
}

/**************************************************************** 表格 ****************************************************************/

.font-36 {
  font-size: 36px;
}

.pl-20 {
  padding-left: 60px !important;
}

.table {
  width: 100%;
  height: 760px;
  /* padding: 10px; */
  box-sizing: border-box;
  overflow-y: auto;
  display: flex;
  align-items: center;
  /* background-color: #FD852E; */
}

.newChartClass {
  width: 100%;
  height: 100%;
}

.leftPart {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.lineOne {
  display: flex;
  align-items: center;
}

.lefPng {
  width: 185px;
  height: 162px;
}

.bgOne {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgOne.png');
  background-size: 100% 100%;
}

.bgTwo {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgTwo.png');
  background-size: 100% 100%;
}

.bgThree {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgThree.png');
  background-size: 100% 100%;
}

.bgFour {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgFour.png');
  background-size: 100% 100%;
}

.bgActive {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgActive.png');
  background-size: 100% 100%;
}

.bgNone {
  background-image: url('@/pages/qyzfCyl/img/cyl/bgNone.png');
  background-size: 100% 100%;
}

.rightCard {
  width: 260px;
  height: 130px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin-left: 98px;
  box-sizing: border-box;
  padding-left: 32px;
}

.jyfxClass {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 5px;
}

.rightPart {
  height: 100%;
  flex: 1;
  /* background-color: aqua; */

  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.zfWrap {
  width: 100%;
  height: 100px;
  background-image: url('@/pages/qyzfCyl/img/cyl/luj.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 25px;
}

.txtS {
  font-size: 32px;
  color: #ffffff;
}

.countCard {
  font-size: 40px;
  color: #ffffff;
  margin: 0 70px 0 100px;
}

.zztChart {
  width: 100%;
  flex: 1;
  /* background-color: #FFC460; */
}

.charOneClass {
  width: 100%;
  height: 100%;
}

/* 年度总体目标标题悬浮样式 */
/* .el-popover {
  background: rgba(3, 26, 50, 0.95);
  min-width: 650px;
  color: #fff;
  font-size: 36px;
  border: 3px solid rgba(5, 145, 243, 0.5);
  padding: 10px 10px 10px 10px;
  display: flex;
  justify-content: center;
} */

/*******************************  迁移后页面  *******************************/
.num_bgactive {
  cursor: pointer;
  width: 250px;
  height: 140px;
  line-height: 60px;
  font-size: 30px;
  padding-left: 20px;
  margin-left: 50px;
  background: url('@/pages/qyzfCyl/img/qyhx/fxyj_rightnum.png') no-repeat;
  background-size: 100% 100%;
}

.num_bg {
  cursor: pointer;
  width: 250px;
  height: 140px;
  line-height: 60px;
  font-size: 30px;
  margin-left: 50px;
  padding-left: 20px;
  background: linear-gradient(-90deg, rgba(35, 160, 255, 0) 0%, rgba(35, 116, 255, 0.6) 100%);
}

.unit {
  color: rgb(203 203 200);
  font-size: 32px;
}

.qyyjqy {
  width: 100%;
}

.qyyjqy_top {
  width: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 38px;
  margin-top: 20px;
  line-height: 60px;
  padding: 15px;
  box-sizing: border-box;
  background: linear-gradient(268deg, rgba(0, 121, 227, 0) 0%, rgba(28, 148, 249, 0.588) 51%, rgba(8, 17, 26, 0) 100%);
  /* opacity: 0.4; */
}

.qyyjqy_top_1 {
  width: 200px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  margin-right: 50px;
  font-size: 54px;
  /* background: url('/static/citybrain/qyhx/images/qyzs-bg.png') no-repeat 0px -20px; */
}

.qyyjqy_top_2 {
  display: flex;
  align-items: center;
  font-size: 54px;
}

.con {
  width: 100%;
  height: 910px;
  /* margin-bottom: 30px; */
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.con > div {
  width: 66%;
}

.cursorP {
  cursor: pointer;
}

.noDataClass {
  width: 100%;
  height: 600px;
  /* background-color: antiquewhite; */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  color: rgb(64, 163, 224);
  /* color: rgb(6, 245, 90); */
}

/**********************************************************************  以下是 经营风险 样式  **********************************************************************/

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
  background: rgba(54, 165, 237, 0);
  -webkit-box-shadow: inset 0 0 5px rgba(54, 165, 237, 0);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}

::-webkit-scrollbar-corner {
  background: rgba(35, 144, 207, 0);
}

.cyzyClass {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 600px;
  /* background-color: antiquewhite; */
}

.tableWrap {
  /* height: 770px; */
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  margin-top: 40px;
  margin-bottom: 40px;
  /* background-color: #FD852E; */
  /* overflow-y: auto; */
  overflow: hidden;
}

/* 表格自动滚动 */
@keyframes rowUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }
}

.el-table__row {
  /* animation: 10s rowUp linear infinite normal;
  -webkit-animation: 10s rowUp linear infinite normal; */
}

.table-animation:hover .el-table__row {
  /* animation-play-state: paused; */
}

.el-table,
.el-table__expanded-cell {
  background-color: transparent !important;
}

.Table ::v-deep .el-table__expanded-cell {
  background-color: transparent !important;
}

.el-input__inner {
  background-color: transparent;
}

.el-table th,
.el-table tr,
.el-table td {
  background-color: transparent;
  border-bottom: none;
}

.el-table__row {
  background-color: rgba(25, 52, 88, 0.2) !important;
}

.el-table tbody tr:hover > td {
  background-color: rgba(25, 52, 88, 0.2) !important;
}

.el-table::before {
  height: 0;
}

.el-table td,
.el-table th.is-leaf {
  border-bottom: none;
}

.el-table td {
  height: 92px;
}

.el-table thead {
  width: 3168px;
  height: 90px;
  background-color: rgba(25, 52, 88, 0.6);
  font-weight: bolder;
  font-size: 32px;
  color: #cde7ff;
}

.el-table th > .cell {
  height: 32px;
  font-size: 32px;
  font-weight: 400;
  color: #dcefffff;
  line-height: 32px;
}

.el-table .cell {
  height: 32px;
  font-size: 32px;
  font-weight: 400;
  color: #dcefff;
  line-height: 32px;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(25, 52, 88, 0.6) !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background: transparent;
}

/*序号列*/
.Table .el-table_1_column_1 {
  /* padding-left: 20px; */
  text-align: center !important;
}

/**********************************************************************  以下是 pageNation 样式  **********************************************************************/

/deep/.pageWrap {
  .el-pagination span:not([class*='suffix']) {
    font-size: 32px;
    height: 64px;
    line-height: 64px;
  }
  .el-pagination__total {
    color: #fff;
  }
  .el-pagination__sizes .el-input {
    width: 250px !important;
  }
  .el-pagination__sizes .el-input__inner {
    width: 250px !important;
    height: 64px;
    line-height: 64px;
    font-size: 32px !important;
    border: 2px solid #215293;
    border-radius: 10px 10px 10px 10px;
    color: #fff;
    background-color: rgba(0, 74, 166, 0.2);
  }
  .el-pagination__jump {
    color: #fff;
  }
  .el-input__suffix {
    color: #fff;
    right: 20px;
  }
  .el-select .el-input .el-select__caret {
    font-size: 32px;
    width: 40px;
    color: #fff;
  }
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: RGBA(95, 123, 150, 1);
    min-width: 64px;
    height: 64px;
    color: #fff;
    margin: 0 10px;
  }

  .el-pagination .btn-next .el-icon,
  .el-pagination .btn-prev .el-icon {
    font-size: 32px;
  }

  .el-pagination.is-background .btn-next.disabled,
  .el-pagination.is-background .btn-next:disabled,
  .el-pagination.is-background .btn-prev.disabled,
  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .el-pager li.disabled {
    color: #c0c4cc;
  }
  .el-pagination__editor.el-input {
    width: 180px;
    margin: 0 20px;
  }

  .el-pagination__editor.el-input .el-input__inner {
    height: 64px;
    line-height: 64px;
    font-size: 32px !important;
    border: 2px solid #215293;
    border-radius: 10px 10px 10px 10px;
    color: #fff;
    background-color: rgba(0, 74, 166, 0.2);
  }
  .el-pager li {
    font-size: 32px;
    line-height: 64px;
    font-weight: 400;
  }

  .el-pager li.btn-quicknext,
  .el-pager li.btn-quickprev {
    line-height: 64px;
  }
}

.pageWrap {
  display: flex;
  flex-direction: row-reverse;
}

/**********************************************************************  自己写的表格样式  **********************************************************************/

.table-head {
  /* width: 100%; */
  height: 92px;
  border-radius: 0px 0px 0px 0px;
  font-weight: 500;
  font-size: 32px;
  color: #cde7ff;
  line-height: 48px;
  display: flex;
  align-items: center;
}

.table-bg-1 {
  background: rgba(10, 183, 255, 0.1) !important;
}

.table-content {
  padding: 32px 0;
  background: #020810;
  border-radius: 0px 0px 0px 0px;
  font-weight: 500;
  font-size: 32px;
  color: #cde7ff;
  display: flex;
  align-items: center;
}

.table-head div {
  text-align: center;
  flex-shrink: 0;
}

.table-content td {
  text-align: center;
  flex-shrink: 0;
}

.tbody {
  width: 100%;
  height: 358px;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.tbody:hover {
  overflow-y: auto;
}

.wid100 {
  width: 100px;
}

.wid240 {
  width: 240px;
}

.wid140 {
  width: 140px;
}

.wid300 {
  width: 300px;
}

.wid360 {
  width: 360px;
}

.wid480 {
  width: 480px;
}

.wid400 {
  width: 390px;
}

.gao {
  color: #ed6c84 !important;
}

.zhong {
  color: #e8bd6c !important;
}

.di {
  color: #a676f2 !important;
}

.qymcClass {
  color: #48b7ef !important;
}

.newWid {
  display: flex;
  justify-content: center;
  align-items: center;
}

.jtClass {
  margin-left: 16px;
}

.overClassP {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-popover {
  background: rgba(3, 26, 50, 0.95);
  min-width: 222px;

  border: 3px solid rgba(5, 145, 243, 0.5);
  padding: 10px 10px 10px 10px;
}

.el-popper[x-placement^='top'] .popper__arrow {
  bottom: -6px;
  left: 50%;
  margin-right: 3px;
  border-top-color: rgba(5, 145, 243, 0.5);
  border-bottom-width: 0;
}

.el-popper[x-placement^='top'] .popper__arrow::after {
  bottom: 1px;
  margin-left: -6px;
  border-top-color: rgba(5, 145, 243, 0.5);
  border-bottom-width: 0;
}

.optionClass {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.optionItem {
  width: 100%;
  color: #fff;
  font-size: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
}

.optionItem:hover {
  color: #91b8f3;
}

.ssqxActiveClass {
  color: #91b8f3 !important;
}
</style>