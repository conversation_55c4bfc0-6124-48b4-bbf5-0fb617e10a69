<body>
  <div id="app" class="container" v-cloak>
    <div class="first-titles">年度总体目标</div>
    <div class="boxContainer">
      <div class="boxOne" v-for="(item,index) in ztmbData" :key="index">

        <!-- <el-popover placement="top-start" width="200" trigger="click" content="二级菜单弹出框">
          <div class="hoverClass">{{item.name}}</div>
          <div class="boxName" slot="reference">{{item.name}}</div>
        </el-popover> -->
        <div class="boxName" slot="reference">{{item.name}}</div>

        <div class="numWrap">
          <div class="number" v-for="(item, i) in item.number" :key="i">
            <div class="numbg" v-if="item!=','&&item!='.'&&item!='-'">
              <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
            </div>
            <div class="numA" v-else>{{item}}</div>
          </div>
          <div class="unitA">{{item.unit}}</div>
        </div>
        <div class="datLine">
          <div class="datLeft">
            <div class="labelClass">目标值：</div>
            <div class="numB">{{item.mbz}}{{item.mbzUnit}}</div>
          </div>
          <div class="datLeft">
            <div class="labelClass">完成度：</div>
            <div class="numC">{{item.wcd}}%</div>
          </div>
        </div>
        <div class="jdChartClass" :id="`hjfb-chart${index+1}`"></div>
      </div>
    </div>

    <div class="first-titles">产业风险预警</div>
    <div class="table">
      <div class="con" style="height: 100%; justify-content: normal">
        <div class="con-item" style="width: 30%; display: flex; flex-direction: column; justify-content: space-evenly">
          <div class="lis_box s-c-white s-font-32 s-flex s-col-center" v-for="(ele,index) in listData">
            <div style="display: flex; flex-direction: column; align-items: center">
              <img @click="openqyfx(ele.tit)" class="cursorP"
                :src="`/static/citybrain/qyhx/images/fxyj_right${index}.png`" alt="" />
            </div>
            <div style="font-size: 52px; font-weight: bold" @click="changeData(index,ele.tit)"
              :class="fxyjIndex1==index&&'num_bgactive'||'num_bg'">
              <span style="font-weight: normal; font-size: 36px; color: #dedede">{{ele.tit}}</span>
              <br />
              <span :style="{'color':index==fxyjIndex1?'rgb(64 163 224)':''}">
                {{Number(ele.num).toLocaleString()}}
              </span>
              <span class="unit">{{ele.unit}}</span>
            </div>
          </div>
        </div>
        <div class="con-item" style="flex: 1">
          <div class="s-flex-1 overHide" style="position: relative">
            <div class="qyyjqy">
              <div class="qyyjqy_top s-row-center">
                <div class="qyyjqy_top_1">总数</div>
                <div class="qyyjqy_top_2">
                  <div v-for="(item,index) in Number(zstotal).toLocaleString()" :key="index" class="s-w7">
                    <span v-if="item==','">,</span>
                    <count-to v-else :start-val="0" :end-val="Number(item)" :duration="3000"></count-to>
                  </div>
                  <span style="margin-left: 10px">{{listData[fxyjIndex1].unit}}</span>
                </div>
              </div>
              <div v-show="fxyjIndex1 == 0" class="cyzyClass">
                <div class="tableWrap">
                  <table>
                    <thead>
                      <tr class="table-head table-bg-1">
                        <th class="wid100">序号</th>
                        <th class="wid240 newWid">
                          <div class="wzTxt">所属区县</div>
                          <el-popover placement="top-start" trigger="hover" content="">
                            <div class="optionClass">
                              <div @click="filterSsqx(item)"
                                :class="{'optionItem':true,'ssqxActiveClass':item==ssqxActive}"
                                v-for="(item,index) in ssqxOption" :key="index">{{item}}</div>
                            </div>
                            <i slot="reference" class="jtClass cursorP el-icon-caret-bottom"></i>
                          </el-popover>
                        </th>
                        <th class="wid240 newWid">
                          <div class="wzTxt">风险详情</div>
                          <el-popover placement="top-start" trigger="hover" content="">
                            <div class="optionClass">
                              <div @click="filterYjnr(item)"
                                :class="{'optionItem':true,'ssqxActiveClass':item==yjnrActive}"
                                v-for="(item,index) in yjnrOption" :key="index">{{item}}</div>
                            </div>
                            <i slot="reference" class="jtClass cursorP el-icon-caret-bottom"></i>
                          </el-popover>
                        </th>
                        <th class="wid140">风险等级</th>
                        <th class="wid400">涉事企业</th>
                        <th class="wid240">涉事时间</th>
                      </tr>
                    </thead>
                    <tbody class="tbody" id="scrollTbody" @mouseover="mouseenterEvent()"
                      @mouseleave="mouseleaveEvent()">
                      <tr :class="{'table-bg-1':index%2!=0,'table-bg-2':index%2==0,'table-content':true}"
                        v-for="(item,index) in jyfxList">
                        <td class="wid100 overClassP">{{index + 1}}</td>
                        <td class="wid240 overClassP">{{item.ssqx}}</td>
                        <td class="wid240 overClassP">{{item.yjnr}}</td>
                        <td
                          :class="{'wid140':true, 'overClassP':true,'gao':item.fxpf == '高','zhong':item.fxpf == '中','di':item.fxpf == '低'}">
                          {{item.fxpf}}</td>
                        <td class="wid400 overClassP cursorP qymcClass" @click="openQyDetail(item)">{{item.qymc}}</td>
                        <td class="wid240 overClassP">{{item.yjsj}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="pageWrap">
                  <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="queryParams.pageNum" :page-sizes="[10, 20, 40]" :page-size="queryParams.pageSize"
                    layout="total, prev, pager, next" :total="total" />
                </div>
              </div>
              <div v-show="fxyjIndex1 != 0" id="echart06" style="width: 100%; height: 600px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: '#app',

      data () {
        return {
          ztmbData: [
            {
              name: '规上总产值',
              number: '911.35',
              unit: '亿元',
              mbz: '4500',
              mbzUnit: '亿元',
              wcd: '20.25%',
            },
            {
              name: '制造业投资增速',
              number: '15',
              unit: '%',
              mbz: '11',
              mbzUnit: '%',
              wcd: '136',
            },
            {
              name: '招引十亿元以上重大项目',
              number: '38',
              unit: '个',
              mbz: '25',
              mbzUnit: '个',
              wcd: '152',
            },
            {
              name: '培育“链主”企业',
              number: '65',
              unit: '家',
              mbz: '40',
              mbzUnit: '家',
              wcd: '162.5',
            },
            {
              name: '开展关键技术难题攻关',
              number: '30',
              unit: '项',
              mbz: '15',
              mbzUnit: '项',
              wcd: '200',
            },
            {
              name: '新增科创平台',
              number: '79',
              unit: '个',
              mbz: '50',
              mbzUnit: '个',
              wcd: '158',
            },
            {
              name: '培育省级以上专精特新和隐形冠军企业',
              number: '-',
              unit: '家',
              mbz: '40',
              mbzUnit: '家',
              wcd: '-',
            },
            {
              name: '新增金华“品字标”企业',
              number: '-',
              unit: '家',
              mbz: '20',
              mbzUnit: '家',
              wcd: '-',
            },
            {
              name: '新增上市过会企业',
              number: '-',
              unit: '家',
              mbz: '6',
              mbzUnit: '家',
              wcd: '-',
            }
          ],
          listData: [
            { tit: '用能风险', unit: '条', num: 0 },
            { tit: '安全生产风险', unit: '家', num: 0 },
            { tit: '信用风险', unit: '家', num: 0 },
            { tit: '外迁风险', unit: '家', num: 0 }
          ],
          fxyjIndex1: 0,
          zstotal: 0,
          total: 0,
          queryParams: {
            pageNum: 1,
            pageSize: 10,
            ssqx: null,
            yjnr: null
            // yjnr: "'用电骤降','用电骤升'"
            // yjnr: "'用电骤升'"
          },
          jyfxList: [],
          ssqxOption: ['东阳市', '义乌市', '兰溪市', '婺城区', '开发区', '武义县', '永康市', '浦江县', '磐安县', '金东区', '市本级'],
          ssqxActive: '',
          yjnrOption: ['用水骤升', '用水骤降', '用电骤升', '用电骤降'],
          yjnrActive: '',
          scrollTbody: null,
          time: null
        }
      },

      async mounted () {
        this.getNdztMb()
        this.getCyfxyj()
        await this.getYnFX()
        this.zstotal = this.listData[0].num
        this.scrollTbody = document.getElementById('scrollTbody')
        this.mouseleaveEvent()
      },

      methods: {
        getNdztMb () {
          $api('csdn_qyhx67', { sstp: '100' }).then(res => {
            this.ztmbData[0].number = res[0].gszcz
            this.ztmbData[0].mbz = res[0].gszczmb
            this.ztmbData[0].wcd = res[0].gszczmbwcd
            // console.log('获取年度总体目标', res, this.ztmbData)

            let arr = this.ztmbData.map(el => {
              // let str = el.wcd.slice(0, -1)
              let str = el.wcd
              return str == '-' ? 0 : Number(str)
            })
            // console.log('完成度', arr)

            arr.forEach((item, index) => {
              this.setChart(`hjfb-chart${index + 1}`, item)
            })
          })
        },

        getCyfxyj () {
          $api('qyhx_cyl_fxyjsltj').then(res => {
            this.listData[1].num = res.find(it => it.ywwd1 == '安全生产风险').tjz
            this.listData[2].num = res.find(it => it.ywwd1 == '信用风险').tjz
            this.listData[3].num = res.find(it => it.ywwd1 == '外迁风险').tjz
          })
        },

        async getYnFX () {
          const that = this
          await axios({
            method: 'post',
            url: baseURL.admApi + '/mis/irs/actuator',
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: sessionStorage.getItem('Authorization'),
            },
            data: {
              name: '金华市城市大脑企业智服产业链用能风险信息',
              url: 'http://dw.jinhua.gov.cn/micoservice-provider/xn94lLa9aYe5df20.htm',
              params: that.queryParams
            },
          }).then(res => {
            console.log('获取用能数据', res)
            if (res.status == 200) {
              this.jyfxList = res.data.data.list
              this.total = res.data.data.total
              this.listData[0].num = res.data.data.total
            }
          })
        },

        async changeData (index, name) {
          // 防止已选中后，重复点击
          if (this.fxyjIndex1 == index) {
            return
          }
          this.fxyjIndex1 = index
          if (this.fxyjIndex1 == 0) {
            this.resetQuery()
            await this.getYnFX()
            this.zstotal = this.listData[0].num
          } else {
            let data = []
            $api('qyhx_cyl_fxyjtjfcyl', { ywwd1: name }).then(res => {
              data = res.map(el => {
                return {
                  name: el.ywwd2,
                  value: Number(el.tjz)
                }
              })
              this.getChart02('echart06', data, this.listData[this.fxyjIndex1].unit)
            })
            this.zstotal = this.listData[this.fxyjIndex1].num
          }
        },

        async filterSsqx (item) {
          if (this.ssqxActive == item) {
            this.ssqxActive = ''
            this.queryParams.ssqx = null
            this.queryParams.pageNum = 1
            await this.getYnFX()
            this.zstotal = this.listData[0].num
          } else {
            this.ssqxActive = item
            this.queryParams.ssqx = item
            this.queryParams.pageNum = 1
            await this.getYnFX()
            this.zstotal = this.listData[0].num
          }
        },

        async filterYjnr (item) {
          if (this.yjnrActive == item) {
            this.yjnrActive = ''
            this.queryParams.yjnr = null
            this.queryParams.pageNum = 1
            await this.getYnFX()
            this.zstotal = this.listData[0].num
          } else {
            this.yjnrActive = item
            this.queryParams.yjnr = `'${item}'`
            this.queryParams.pageNum = 1
            await this.getYnFX()
            this.zstotal = this.listData[0].num
          }
        },

        handleCurrentChange (val) {
          this.queryParams.pageNum = val
          this.getYnFX()
        },

        handleSizeChange (val) {
          this.queryParams.pageSize = val
          if (this.queryParams.pageNum * val > this.total) {
            this.queryParams.pageNum = 1
          }
          this.getYnFX()
        },

        mouseenterEvent () {
          clearInterval(this.time)
        },

        mouseleaveEvent () {
          console.log('执行了吗', this.scrollTbody.scrollTop, this.scrollTbody.scrollHeight)
          this.time = setInterval(() => {
            this.scrollTbody.scrollTop += 5
            if (this.scrollTbody.scrollTop >= this.scrollTbody.scrollHeight - this.scrollTbody.offsetHeight - 3) {
              this.scrollTbody.scrollTop = 0
            }
          }, 100)
        },

        resetQuery () {
          this.queryParams = {
            pageNum: 1,
            pageSize: 10,
            ssqx: null,
            yjnr: null
          }
        },

        openqyfx (name) {
          window.parent.lay.openIframe({
            type: 'openIframe',
            name: 'qyzf-fxyj-dialog',
            src: baseURL.url + '/static/citybrain/qyhx/commont/qyzf-fxyj-dialog.html',
            left: '0',
            top: '0',
            width: '7680px',
            height: '2160px',
            zIndex: '500',
            argument: {
              tit: name,
            },
          })
        },

        openQyDetail (item) {
          axios({
            method: 'post',
            url: baseURL.admApi + '/mis/irs/actuator',
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: sessionStorage.getItem('Authorization')
            },
            data: {
              name: '金华企业画像企业搜索',
              url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012229/dataSharing/0cSJbwb7r4uh7H4e.htm',
              params: {
                // tyshxydm: item.tyshxydm
                name: item.qymc
              } // '91330701MA8G4H8B5N',
            },
          }).then(res => {
            let arr = res.data.datas.data ? res.data.datas.data : []
            window.parent.lay.openIframe({
              type: 'openIframe',
              name: 'qyhx-xq-index',
              src: baseURL.url + '/static/citybrain/qyhx/pages/qyhx-xq-index.html',
              left: '0px',
              top: '0',
              width: '7680px',
              height: '2160px',
              zIndex: '999',
              argument: {
                allMessage: arr,
                status: 'detail'
              }
            })
          })
        },

        getChart02 (id, chartData, unit) {
          let myEc = echarts.init(document.getElementById(id))
          let copyData = JSON.parse(JSON.stringify(chartData)).sort(function (a, b) {
            return b.value - a.value
          })
          let maxNum1 = (copyData[0] && copyData[0].value) || 0
          let maxNum2 = (copyData[1] && copyData[1].value) || 0
          let maxNum3 = (copyData[2] && copyData[2].value) || 0
          let data = copyData.map(a => a.value)
          let colors = data.length > 3 ? data.map(a => a == maxNum1 ? '#e33c2f' : a == maxNum2 ? '#fcc053' : a == maxNum3 ? '#f58b1a' : '#1677FF') : ['#e33c2f', '#fcc053', '#f58b1a']
          let option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(51, 51, 51, 0.7)',
              borderWidth: 0,
              axisPointer: {
                type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
              },
              confine: true,
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
              formatter: (e) => {
                let main = e.filter((a) => a.value != '-')[0]
                return `${main.name}<br/>${main.seriesName}：${main.value + unit}`
              },
            },
            grid: {
              left: '5%',
              right: '5%',
              top: '10%',
              bottom: '5%',
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                // data: chartData.map(a => a.name),
                data: copyData.map(a => a.name),
                axisLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,1)', // 颜色
                    width: 3, // 粗细
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  // rotate: chartData.length > 9 ? 45 : 0,
                  // rotate: 0,
                  rotate: -30,
                  // width: 100,//将内容的宽度固定
                  // overflow: 'truncate',//超出的部分截断
                  // overflow: 'break',//超出的部分截断
                  // ellipsis: '...',//截断的部分用...代替
                  textStyle: {
                    color: '#D6E7F9',
                    fontSize: 30,
                  },
                  formatter: (value, index) => {
                    if (value.length > 3) {
                      return value.substring(0, 3) + '...'
                    } else {
                      return value
                    }
                  },
                },
              },
            ],
            yAxis: [
              {
                name: '',
                type: 'value',
                // min: 0,
                nameTextStyle: {
                  fontSize: 32,
                  color: '#D6E7F9',
                  padding: 5,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)',
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 32,
                    color: '#D6E7F9',
                  },
                },
              },
            ],
            series: [
              {
                name: '预警企业',
                type: 'bar',
                barWidth: chartData.length > 3 ? '40%' : 50,
                label: {
                  normal: {
                    show: true,
                    position: 'top',
                    fontSize: 27,
                    color: '#fff',
                  },
                },
                itemStyle: {
                  normal: {
                    color: (param) => {
                      return {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: colors[param.dataIndex],
                          },
                          {
                            offset: 1,
                            color: '#1677FF00',
                          },
                        ],
                      }
                    },
                    barBorderRadius: 4,
                  },
                },
                // data: chartData.map((a) => a.value),
                data: copyData.map((a) => a.value),
              },
            ],
          }
          myEc.setOption(option)
          myEc.getZr().on('mousemove', (param) => {
            myEc.getZr().setCursorStyle('default')
          })
        },

        setChart (id, chartData) {
          let myChart = echarts.init(document.getElementById(id))
          let option = {
            grid: {
              top: 0,
              left: '0%',
              right: '0%',
              bottom: 0,
              containLabel: true,
            },
            xAxis: {
              type: 'value',
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              max: 100,
            },
            yAxis: {
              type: 'category',
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
            },
            series: [
              {
                type: 'bar',
                itemStyle: {
                  normal: {
                    color: {
                      type: "linear",
                      x: 1,
                      y: 1,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "rgba(49, 227, 255, 1)" // 0% 处的颜色
                        },
                        {
                          offset: 0.5,
                          color: "RGBA(7, 103, 190, 1)" // 0% 处的颜色
                        },
                        {
                          offset: 0.9,
                          color: "RGBA(2, 129, 231, 1)" // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "RGBA(15, 59, 123, 1)" // 100% 处的颜色
                        }
                      ],
                      globalCoord: false // 缺省为 false
                    },
                    opacity: 0.7,
                    barBorderRadius: 4,
                  },
                },
                label: {
                  show: false,
                  formatter: '',
                  backgroundColor: 'RGBA(25, 194, 254, 1)',
                  width: 6,
                  height: 6,
                  position: 'right',
                  offset: [-14, 0],
                  borderWidth: 5,
                  borderColor: 'RGBA(25, 194, 254, 1)',
                  borderRadius: 5,
                  shadowColor: 'RGBA(25, 194, 254, 1)',
                  shadowBlur: 20
                },
                showBackground: true,
                backgroundStyle: {
                  color: 'RGBA(14, 58, 119, 1)',
                },
                silent: true,
                barWidth: 15,
                barGap: '-100%', // Make series be overlap
                data: [chartData],
              },
              {
                type: 'lines',
                coordinateSystem: 'cartesian2d',
                data: [chartData].map((item, index) => {
                  return {
                    coords: [
                      [0, index],
                      [item - 4, index]
                    ]
                  }
                }),
                effect: {
                  show: true,
                  period: 2.5,
                  trailLength: 0,
                  symbolSize: 20,
                  symbol: "pin",
                  loop: true,
                  color: "RGBA(25, 194, 254, 1)"
                },
                lineStyle: {
                  width: 0,
                  opacity: 0,
                  curveness: 0
                },
                z: 99
              }
            ]
          }
          myChart.setOption(option, true)
        },

      }
    })
  </script>
</body>

</html>